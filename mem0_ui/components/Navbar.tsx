"use client";

import { HiH<PERSON>, HiMiniRectangleStack } from "react-icons/hi2";
import { FiRefreshCcw } from "react-icons/fi";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { Settings, Users, Activity, Menu, X } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { CreateMemoryDialog } from "@/app/memories/components/CreateMemoryDialog";
import { useMemoriesApi } from "@/hooks/useMemoriesApi";
import { useStats } from "@/hooks/useStats";

import { useConfig } from "@/hooks/useConfig";
import { useUserManagement } from "@/hooks/useUserManagement";


export function Navbar() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const memoriesApi = useMemoriesApi();
  const statsApi = useStats();
  const configApi = useConfig();
  const userManagementApi = useUserManagement();

  // Define route matchers with typed parameter extraction
  const routeBasedFetchMapping = [
    {
      match: /^\/memory\/([^/]+)$/,
      getFetchers: (params: Record<string, string>) => [
        () => memoriesApi.fetchMemoryById(params.memory_id),
        () => memoriesApi.fetchAccessLogs(params.memory_id),
        () => memoriesApi.fetchRelatedMemories(params.memory_id),
      ],
    },
    {
      match: /^\/memories$/,
      getFetchers: () => [memoriesApi.fetchMemories],
    },
    {
      match: /^\/$/,
      getFetchers: () => [statsApi.fetchStats, memoriesApi.fetchMemories],
    },
    {
      match: /^\/settings$/,
      getFetchers: () => [configApi.testConnection],
    },
    {
      match: /^\/users$/,
      getFetchers: () => [userManagementApi.fetchUsers],
    },
    {
      match: /^\/monitoring$/,
      getFetchers: () => [],
    },
  ];

  const getFetchersForPath = (path: string) => {
    for (const route of routeBasedFetchMapping) {
      const match = path.match(route.match);
      if (match) {
        if (route.match.source.includes("memory")) {
          return route.getFetchers({ memory_id: match[1] });
        }
        if (route.match.source.includes("app")) {
          return route.getFetchers({ app_id: match[1] });
        }
        return route.getFetchers({});
      }
    }
    return [];
  };

  const handleRefresh = async () => {
    const fetchers = getFetchersForPath(pathname);
    await Promise.allSettled(fetchers.map((fn) => fn()));
  };

  const isActive = (href: string) => {
    if (href === "/") return pathname === href;
    return pathname.startsWith(href.substring(0, 5));
  };

  const activeClass = "bg-zinc-800 text-white border-zinc-600";
  const inactiveClass = "text-zinc-300";

  // 导航链接数据
  const navLinks = [
    { href: "/", icon: HiHome, label: "Dashboard" },
    { href: "/memories", icon: HiMiniRectangleStack, label: "Memories" },
    { href: "/users", icon: Users, label: "Users" },
    { href: "/monitoring", icon: Activity, label: "Monitoring" },
    { href: "/settings", icon: Settings, label: "Settings" },
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-zinc-800 bg-zinc-950/95 backdrop-blur supports-[backdrop-filter]:bg-zinc-950/60">
      <div className="container-responsive flex h-14 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2 hover-lift" onClick={closeMobileMenu}>
          <Image src="/logo.svg" alt="OpenMemory" width={26} height={26} />
          <span className="text-responsive-lg font-medium">OpenMemory</span>
        </Link>

        {/* Navigation Links */}
        <div className="flex items-center gap-2">
          {navLinks.map(({ href, icon: Icon, label }) => (
            <Link key={href} href={href}>
              <Button
                variant="outline"
                size="sm"
                className={`flex items-center gap-2 border-none ${
                  isActive(href) ? activeClass : inactiveClass
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden lg:inline">{label}</span>
              </Button>
            </Link>
          ))}
        </div>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-zinc-700/50 bg-zinc-900 hover:bg-zinc-800"
          >
            <FiRefreshCcw className="transition-transform duration-300 group-hover:rotate-180" />
            <span className="hidden lg:inline">Refresh</span>
          </Button>
          <CreateMemoryDialog />
        </div>
      </div>
    </header>
  );
}
