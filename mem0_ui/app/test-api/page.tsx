'use client';

import { useState, useEffect } from 'react';

import { realMem0Client } from '@/lib/mem0-client/realClient';

export default function TestAPIPage() {
  const [status, setStatus] = useState<string>('Loading...');
  const [memories, setMemories] = useState<any[]>([]);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const testAPI = async () => {
      try {
        // 测试健康检查
        const health = await realMem0Client.healthCheck();
        console.log('Health check:', health);
        
        // 测试获取记忆
        const memoriesResponse = await realMem0Client.getMemories({ limit: 5 });
        console.log('Memories response:', memoriesResponse);
        
        setMemories(memoriesResponse.memories);
        setStatus('API working correctly!');
      } catch (err) {
        console.error('API test failed:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setStatus('API test failed');
      }
    };

    testAPI();
  }, []);

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-[#00d4aa]">
          Mem0 API Test Page
        </h1>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">API Status</h2>
          <div className={`p-4 rounded-lg ${
            status.includes('working') ? 'bg-green-900/20 border border-green-500' : 
            status.includes('failed') ? 'bg-red-900/20 border border-red-500' : 
            'bg-gray-900/20 border border-gray-500'
          }`}>
            <p className="text-lg">{status}</p>
            {error && (
              <p className="text-red-400 mt-2">Error: {error}</p>
            )}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Sample Memories</h2>
          <div className="space-y-4">
            {memories.length > 0 ? (
              memories.map((memory) => (
                <div key={memory.id} className="bg-gray-900/50 p-4 rounded-lg border border-gray-700">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm text-gray-400">ID: {memory.id}</span>
                    <span className="text-sm text-gray-400">
                      {memory.created_at ? new Date(memory.created_at).toLocaleDateString() : 'No date'}
                    </span>
                  </div>
                  <p className="text-white mb-2">{memory.memory}</p>
                  <div className="flex flex-wrap gap-2">
                    {memory.categories?.map((category: string) => (
                      <span key={category} className="px-2 py-1 bg-[#00d4aa]/20 text-[#00d4aa] rounded text-xs">
                        {category}
                      </span>
                    ))}
                  </div>
                  {memory.user_id && (
                    <p className="text-xs text-gray-500 mt-2">User: {memory.user_id}</p>
                  )}
                  {memory.agent_id && (
                    <p className="text-xs text-gray-500">Agent: {memory.agent_id}</p>
                  )}
                </div>
              ))
            ) : (
              <p className="text-gray-400">No memories loaded yet...</p>
            )}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">API Endpoints</h2>
          <div className="bg-gray-900/50 p-4 rounded-lg border border-gray-700">
            <p className="text-sm text-gray-300 mb-2">Available endpoints:</p>
            <ul className="text-sm text-gray-400 space-y-1">
              <li>• GET /health - Health check</li>
              <li>• GET /v1/memories/ - Get memories</li>
              <li>• POST /v1/memories/ - Create memory</li>
              <li>• GET /v1/memories/{'{id}'}/ - Get single memory</li>
              <li>• PUT /v1/memories/{'{id}'}/ - Update memory</li>
              <li>• DELETE /v1/memories/{'{id}'}/ - Delete memory</li>
              <li>• POST /v1/memories/search/ - Search memories</li>
              <li>• PUT /v1/batch/ - Batch update</li>
              <li>• DELETE /v1/batch/ - Batch delete</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
